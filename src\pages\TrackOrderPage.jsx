import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import {
  Search,
  ExternalLink,
  Info,
  AlertCircle,
  Copy,
  Mail,
  Check
} from 'lucide-react';

export default function TrackOrderPage() {
  const [awbNumber, setAwbNumber] = useState('');
  const [isValidAwb, setIsValidAwb] = useState(true);
  const [isCopied, setIsCopied] = useState(false);

  const handleAwbChange = (e) => {
    const value = e.target.value.trim();
    setAwbNumber(value);
    setIsValidAwb(value.length === 0 || /^[A-Za-z0-9]{8,15}$/.test(value));
  };

  const handleTrackOrder = () => {
    if (awbNumber && isValidAwb) {
      const trackingUrl = `https://www.shiprocket.in/shipment-tracking/?awb=${awbNumber}`;
      window.open(trackingUrl, '_blank', 'noopener,noreferrer');
    }
  };

  const handleCopyAwb = async () => {
    try {
      await navigator.clipboard.writeText(awbNumber);
      setIsCopied(true);
      setTimeout(() => setIsCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy AWB number:', err);
    }
  };



  const trackingGuide = [
    {
      step: '01',
      title: 'Find Your AWB Number',
      description: 'Check your email for the shipping confirmation. Your AWB number will be provided once your order is shipped.'
    },
    {
      step: '02',
      title: 'Enter AWB Number',
      description: 'Copy and paste your AWB number in the tracking field. Ensure it\'s entered correctly without spaces.'
    },
    {
      step: '03',
      title: 'Track on Shiprocket',
      description: 'Click "Track Order" to view real-time updates on Shiprocket\'s official tracking page.'
    },
    {
      step: '04',
      title: 'Get Live Updates',
      description: 'View detailed tracking information including current location and estimated delivery time.'
    }
  ];

  return (
      <div className="min-h-screen bg-black text-gray-300" style={{ backgroundColor: '#000000 !important' }}>
        {/* Back to Home Button - Fixed Position */}
        {/*<div*/}
        {/*    style={{*/}
        {/*      position: 'fixed',*/}
        {/*      top: '80px',*/}
        {/*      left: '16px',*/}
        {/*      zIndex: 99999,*/}
        {/*      transform: 'translate3d(0, 0, 0)'*/}
        {/*    }}*/}
        {/*>*/}
        {/*  <Link*/}
        {/*      to="/"*/}
        {/*      className="inline-flex items-center gap-2 text-gray-300 hover:text-white transition-colors duration-300 group"*/}
        {/*  >*/}
        {/*    <ArrowLeft size={18} className="group-hover:-translate-x-1 transition-transform" />*/}
        {/*    <span className="hidden sm:inline">Back to Home</span>*/}
        {/*  </Link>*/}
        {/*</div>*/}

        {/* Hero Section */}
        <div className="relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-gray-900/30 via-black to-gray-900/20"></div>

          <div className="relative z-10 max-w-6xl mx-auto px-6 py-16 md:py-20">
            <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8 }}
                className="text-center"
            >

              <h1 className="text-4xl md:text-5xl lg:text-6xl font-light text-white mb-4 md:mb-6 tracking-wide">
                Track Your Order
              </h1>
              <p className="text-lg md:text-xl text-gray-400 max-w-2xl mx-auto leading-relaxed px-4">
                Enter your AWB number to get real-time updates on your shipment status and delivery progress.
              </p>
            </motion.div>
          </div>
        </div>

        {/* Main Content */}
        <div className="max-w-6xl mx-auto px-4 md:px-6 pb-16 md:pb-20">

          {/* AWB Input Section */}
          <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="mb-16 md:mb-20"
          >
            <div className="max-w-lg mx-auto">
              <div className="bg-[#1a1a1a] backdrop-blur-sm rounded-2xl md:rounded-3xl p-6 md:p-8 border border-gray-800/60 shadow-2xl" style={{ backgroundColor: 'rgba(17, 24, 39, 0.7) !important' }}>
                <h2 className="text-xl md:text-2xl font-medium text-white mb-6 md:mb-8 text-center">Enter AWB Number</h2>

                <div className="space-y-4 md:space-y-6">
                  <div className="relative">
                    <input
                        type="text"
                        value={awbNumber}
                        onChange={handleAwbChange}
                        placeholder="Enter your AWB tracking number"
                        className={`w-full px-4 md:px-6 py-3 md:py-4 bg-gray-800/60 border rounded-xl md:rounded-2xl text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-orange-500/50 focus:border-orange-500/50 transition-all duration-300 text-sm md:text-base ${
                            isValidAwb ? 'border-gray-700/60' : 'border-red-500/50'
                        }`}
                    />
                    {awbNumber && (
                        <button
                            onClick={handleCopyAwb}
                            className="absolute right-3 md:right-4 top-1/2 transform -translate-y-1/2 p-2 hover:bg-gray-700/50 rounded-lg transition-colors"
                            title={isCopied ? 'Copied!' : 'Copy AWB number'}
                        >
                          {isCopied ?
                              <Check size={16} className="text-green-400 md:w-[18px] md:h-[18px]" /> :
                              <Copy size={16} className="text-gray-400 md:w-[18px] md:h-[18px]" />
                          }
                        </button>
                    )}
                  </div>

                  {!isValidAwb && (
                      <div className="flex items-center gap-2 text-red-400 text-sm">
                        <AlertCircle size={16} />
                        Please enter a valid AWB number (8-15 characters)
                      </div>
                  )}

                  <button
                      onClick={handleTrackOrder}
                      disabled={!awbNumber || !isValidAwb}
                      className="w-full bg-gradient-to-r from-orange-500 to-orange-600 text-white px-6 py-3 md:py-4 rounded-xl md:rounded-2xl font-medium hover:from-orange-600 hover:to-orange-700 transition-all duration-300 hover:shadow-lg hover:shadow-orange-500/25 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2 md:gap-3 text-sm md:text-base"
                  >
                    {/*<Search size={18} className="md:w-5 md:h-5" />*/}
                    Track Order
                    <ExternalLink size={14} className="md:w-4 md:h-4" />
                  </button>
                </div>
              </div>
            </div>
          </motion.div>

          {/* How to Track Section */}
          <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="max-w-4xl mx-auto mb-12 md:mb-16"
          >
            <div className="bg-[#1a1a1a] backdrop-blur-sm rounded-2xl md:rounded-3xl p-6 md:p-8 border border-gray-800/60 shadow-xl"
                style={{ backgroundColor: 'rgba(17, 24, 39, 0.6) !important' }}
            >
              <div className="flex items-center gap-3 mb-8 md:mb-10">
                <div className="w-10 h-10 md:w-12 md:h-12 bg-orange-500/10 rounded-xl flex items-center justify-center" style={{ backgroundColor: 'rgba(249, 115, 22, 0.1) !important' }}>
                  <Search size={20} className="text-orange-400 md:w-6 md:h-6" />
                </div>
                <h3 className="text-xl md:text-2xl font-medium text-white">How to Track</h3>
              </div>

              <div className="space-y-6 md:space-y-8">
                {trackingGuide.map((item, index) => (
                    <motion.div
                        key={index}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.6, delay: 0.1 * index }}
                        className="relative group"
                    >
                      <div className="flex gap-4 md:gap-5 p-4 md:p-5 rounded-xl bg-[#1a1a1a] border border-gray-700/30 hover:border-orange-500/30 transition-all duration-300 hover:bg-gray-800/50">
                        <div className="flex-shrink-0">
                          <div className="w-12 h-12 md:w-14 md:h-14 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center shadow-lg shadow-orange-500/20">
                            <span className="text-white font-bold text-sm md:text-base">{item.step}</span>
                          </div>
                        </div>
                        <div className="pt-1 md:pt-2 min-w-0 flex-1">
                          <h4 className="text-base md:text-lg font-semibold text-white mb-2 md:mb-3">{item.title}</h4>
                          <p className="text-gray-400 leading-relaxed text-sm md:text-base">{item.description}</p>
                        </div>
                      </div>
                    </motion.div>
                ))}
              </div>
            </div>
          </motion.div>

          {/* Information Cards */}
          <div className="grid md:grid-cols-2 gap-6 md:gap-8 mt-8 md:mt-12">

            {/* Important Information */}
            <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.8 }}
                className="bg-[#1a1a1a] backdrop-blur-sm rounded-2xl md:rounded-3xl p-6 md:p-8 border border-gray-800/60 shadow-xl"
                style={{ backgroundColor: 'rgba(17, 24, 39, 0.6) !important' }}
            >
              <div className="flex items-start gap-3 md:gap-4 mb-4 md:mb-6">
                <div className="w-8 h-8 md:w-10 md:h-10 bg-orange-500/10 rounded-xl flex items-center justify-center" style={{ backgroundColor: 'rgba(249, 115, 22, 0.1) !important' }}>
                  <Info size={18} className="text-orange-400 md:w-5 md:h-5" />
                </div>
                <h4 className="text-lg md:text-xl font-medium text-white">Important Information</h4>
              </div>

              <ul className="space-y-3 md:space-y-4 text-gray-400">
                <li className="flex items-start gap-3">
                  <div className="w-1.5 h-1.5 bg-orange-500 rounded-full mt-2 md:mt-2.5 flex-shrink-0"></div>
                  <span className="text-sm md:text-base">AWB numbers are provided via email once your order is shipped</span>
                </li>
                <li className="flex items-start gap-3">
                  <div className="w-1.5 h-1.5 bg-orange-500 rounded-full mt-2 md:mt-2.5 flex-shrink-0"></div>
                  <span className="text-sm md:text-base">Tracking information may take 2-4 hours to update after shipment</span>
                </li>
                <li className="flex items-start gap-3">
                  <div className="w-1.5 h-1.5 bg-orange-500 rounded-full mt-2 md:mt-2.5 flex-shrink-0"></div>
                  <span className="text-sm md:text-base">Delivery times may vary based on location and weather conditions</span>
                </li>
                <li className="flex items-start gap-3">
                  <div className="w-1.5 h-1.5 bg-orange-500 rounded-full mt-2 md:mt-2.5 flex-shrink-0"></div>
                  <span className="text-sm md:text-base">Contact our support team with your order number for any tracking issues</span>
                </li>
              </ul>
            </motion.div>

            {/* Contact Support */}
            <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 1.0 }}
                className="bg-[#1a1a1a] backdrop-blur-sm rounded-2xl md:rounded-3xl p-6 md:p-8 border border-gray-800/60 shadow-xl"
                style={{ backgroundColor: 'rgba(17, 24, 39, 0.6) !important' }}
            >
              <div className="flex items-start gap-3 md:gap-4 mb-4 md:mb-6">
                <div className="w-8 h-8 md:w-10 md:h-10 bg-orange-500/10 rounded-xl flex items-center justify-center" style={{ backgroundColor: 'rgba(249, 115, 22, 0.1) !important' }}>
                  <Mail size={18} className="text-orange-400 md:w-5 md:h-5" />
                </div>
                <h4 className="text-lg md:text-xl font-medium text-white">Need Help?</h4>
              </div>

              <p className="text-gray-400 mb-6 md:mb-8 leading-relaxed text-sm md:text-base">
                If you're having trouble tracking your order or need assistance, our support team is here to help.
              </p>

              <button className="w-full bg-gradient-to-r from-orange-500 to-orange-600 text-white px-6 py-3 md:py-4 rounded-xl md:rounded-2xl font-medium hover:from-orange-600 hover:to-orange-700 transition-all duration-300 hover:shadow-lg hover:shadow-orange-500/25 flex items-center justify-center gap-2 text-sm md:text-base">
                <Mail size={16} className="md:w-[18px] md:h-[18px]" />
                Contact Support
              </button>

              <div className="flex items-center justify-center gap-4 md:gap-6 mt-4 md:mt-6 pt-4 md:pt-6 border-t border-gray-800/60">
                {/*<div className="flex items-center gap-2 text-gray-500 text-xs md:text-sm">*/}
                {/*  <Phone size={12} className="md:w-[14px] md:h-[14px]" />*/}
                {/*  <span>24/7 Support</span>*/}
                {/*</div>*/}
                {/*<div className="flex items-center gap-2 text-gray-500 text-xs md:text-sm">*/}
                {/*  <Globe size={12} className="md:w-[14px] md:h-[14px]" />*/}
                {/*  <span>Global Service</span>*/}
                {/*</div>*/}
              </div>
            </motion.div>
          </div>

        </div>
      </div>

  );
}