import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  User, 
  Edit3, 
  Phone, 
  Mail, 
  Calendar, 
  MapPin, 
  Heart, 
  ShoppingBag, 
  Package, 
  Settings, 
  LogOut,
  Camera,
  Save,
  X,
  AlertCircle,
  CheckCircle,
  Shirt,
  Star,
  Award,
  TrendingUp
} from 'lucide-react';
import { useAuth } from '../context/AuthContext';
import { useNavigate } from 'react-router-dom';
import { useWishlist } from '../context/WishlistContext';
import { useCart } from '../context/CartContext';
import { useLogout } from '../hooks/useLogout';

const ProfilePage = () => {
  const { user, updateProfile, isLoading, error, refreshProfile, dispatch } = useAuth();
  const { totalWishlistItems } = useWishlist();
  const { totalItems } = useCart();
  const { handleCompleteLogout } = useLogout();
  const navigate = useNavigate();

  const [isEditing, setIsEditing] = useState(false);
  const [editForm, setEditForm] = useState({
    first_name: '',
    last_name: '',
    email: '',
    date_of_birth: '',
    gender: '',
    shipping_address: {
      line1: '',
      line2: '',
      city: '',
      state: '',
      postal_code: '',
      country: 'India'
    }
  });
  const [updateSuccess, setUpdateSuccess] = useState(false);
  const [localError, setLocalError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');

  // Initialize form with user data
  useEffect(() => {
    if (user) {
      // Extract shipping address from user data
      const shippingAddress = user.shipping_address || {};
      
      setEditForm({
        first_name: user.first_name || '',
        last_name: user.last_name || '',
        email: user.email || '',
        date_of_birth: user.date_of_birth || '',
        gender: user.gender || '',
        shipping_address: {
          line1: shippingAddress.line1 || shippingAddress.street || '',
          line2: shippingAddress.line2 || '',
          city: shippingAddress.city || '',
          state: shippingAddress.state || '',
          postal_code: shippingAddress.postal_code || shippingAddress.zip || '',
          country: shippingAddress.country || 'India'
        }
      });
    }
  }, [user]);

  // Refresh profile on mount
  useEffect(() => {
    refreshProfile();
  }, []);

  // Handle form input changes
  const handleInputChange = (field, value) => {
    // Clear any error messages when user starts typing
    if (localError) {
      setLocalError('');
    }
    
    // Handle nested address fields
    if (field.startsWith('shipping_address.')) {
      const addressField = field.split('.')[1];
      setEditForm(prev => ({
        ...prev,
        shipping_address: {
          ...prev.shipping_address,
          [addressField]: value
        }
      }));
    } else {
      // Handle regular fields
      setEditForm(prev => ({
        ...prev,
        [field]: value
      }));
    }
  };

  // Handle save profile
  const handleSaveProfile = async () => {
    try {
      // Clear any previous errors
      setLocalError('');
      
      // Validate required fields
      const requiredFields = ['first_name', 'last_name', 'email', 'gender'];
      const missingFields = requiredFields.filter(field => !editForm[field]);
      
      // Validate address fields
      const requiredAddressFields = ['line1', 'city', 'state', 'postal_code', 'country'];
      const missingAddressFields = requiredAddressFields.filter(
        field => !editForm.shipping_address[field]
      );
      
      if (missingFields.length > 0 || missingAddressFields.length > 0) {
        // Format field names for display
        const formattedFields = missingFields.map(field => {
          return field.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
        });
        
        // Format address field names
        const formattedAddressFields = missingAddressFields.map(field => {
          return `Address ${field.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}`;
        });
        
        // Combine all missing fields
        const allMissingFields = [...formattedFields, ...formattedAddressFields];
        
        throw new Error(`Please fill in all required fields: ${allMissingFields.join(', ')}`);
      }
      
      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(editForm.email)) {
        throw new Error('Please enter a valid email address');
      }
      
      // Show loading state
      dispatch({ type: 'AUTH_UPDATE_PROFILE_START' });
      
      // Map shipping_address to address_data for backend compatibility
      const payload = {
        ...editForm,
        address_data: {
          address_line_1: editForm.shipping_address.line1,
          address_line_2: editForm.shipping_address.line2,
          city: editForm.shipping_address.city,
          state: editForm.shipping_address.state,
          postal_code: editForm.shipping_address.postal_code,
          country: editForm.shipping_address.country,
          first_name: editForm.first_name,
          last_name: editForm.last_name
        }
      };
      delete payload.shipping_address;
      console.log('DEBUG: Submitting profile update with payload:', payload);
      try {
        const result = await updateProfile(payload);
        
        if (result.success) {
          setIsEditing(false);
          setUpdateSuccess(true);
          
          // Set success message
          setSuccessMessage('Profile updated successfully!');
          
          // Clear success message after 3 seconds
          setTimeout(() => {
            setUpdateSuccess(false);
            setSuccessMessage('');
          }, 3000);
          
          // Refresh the profile to get the latest data
          await refreshProfile();
        }
      } catch (apiError) {
        // Handle API-specific errors
        console.error('API Error:', apiError);
        
        // Format the error message for display
        let errorMessage = apiError.message || 'Failed to update profile. Please try again.';
        
        // If it's a server error, provide a more user-friendly message
        if (errorMessage.includes('Server error')) {
          errorMessage = 'The server encountered an error. Please try again later.';
        }
        
        throw new Error(errorMessage);
      }
    } catch (error) {
      // Display error message
      const errorMessage = error.message || 'Failed to update profile. Please try again.';
      
      // Use the AuthContext's dispatch if available
      if (typeof dispatch === 'function') {
        dispatch({
          type: 'AUTH_UPDATE_PROFILE_FAILURE',
          payload: { error: errorMessage }
        });
      }
      
      // Also set local error state as a fallback
      setLocalError(errorMessage);
      console.error('Profile update error:', errorMessage);
    }
  };

  // Handle logout
  const handleLogout = async () => {
    await handleCompleteLogout('/');
  };

  // Format phone number for display
  const formatPhoneDisplay = (phone) => {
    if (!phone) return '';
    const cleaned = phone.replace(/\D/g, '');
    if (cleaned.length === 12 && cleaned.startsWith('91')) {
      const number = cleaned.slice(2);
      return `+91 ${number.slice(0, 5)} ${number.slice(5)}`;
    }
    return phone;
  };

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return '';
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (!user) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center">
          <div className="relative">
            <div className="w-20 h-20 border-4 border-gray-800 rounded-full animate-pulse mx-auto mb-4" />
            <div className="absolute inset-0 w-20 h-20 border-4 border-transparent border-t-blue-500 rounded-full animate-spin mx-auto" />
          </div>
          <p className="text-gray-400 text-lg font-medium">Loading your profile...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-black relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-500/5 rounded-full blur-3xl animate-pulse" />
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-purple-500/5 rounded-full blur-3xl animate-pulse" />
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-r from-blue-500/3 to-purple-500/3 rounded-full blur-3xl" />
      </div>

      <div className="relative z-10 pt-20 pb-12">
        <div className="container mx-auto px-4 max-w-6xl">
          {/* Success Message */}
          <AnimatePresence>
            {updateSuccess && successMessage && (
              <motion.div
                initial={{ opacity: 0, y: -50, scale: 0.95 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                exit={{ opacity: 0, y: -50, scale: 0.95 }}
                className="fixed top-24 left-1/2 transform -translate-x-1/2 z-50 mb-6 p-4 bg-gradient-to-r from-green-500/20 to-emerald-500/20 backdrop-blur-xl border border-green-500/30 rounded-2xl text-green-400 flex items-center gap-3 shadow-2xl"
              >
                <div className="w-8 h-8 bg-green-500/20 rounded-full flex items-center justify-center">
                  <CheckCircle size={18} />
                </div>
                <span className="font-medium">{successMessage}</span>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Profile Header Card */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            className="relative mb-8"
          >
            <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 via-purple-500/10 to-pink-500/10 rounded-3xl blur-xl" />
            <div className="relative bg-black/40 backdrop-blur-xl border border-gray-800/50 rounded-3xl p-8 shadow-2xl">
              <div className="flex flex-col lg:flex-row items-start lg:items-center gap-8">
                {/* Enhanced Profile Image */}
                <div className="relative group">
                  <div className="absolute -inset-1 bg-gradient-to-br from-blue-500 via-purple-500 to-pink-500 rounded-full blur opacity-30 group-hover:opacity-50 transition-opacity duration-300" />
                  <div className="relative w-28 h-28 lg:w-32 lg:h-32 bg-gradient-to-br from-blue-600 via-purple-600 to-pink-600 rounded-full flex items-center justify-center overflow-hidden shadow-2xl">
                    {user.profile_image ? (
                      <img
                        src={user.profile_image}
                        alt="Profile"
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <User size={40} className="text-white" />
                    )}
                  </div>
                  <motion.button 
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.95 }}
                    className="absolute -bottom-2 -right-2 w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-500 hover:to-purple-500 rounded-full flex items-center justify-center transition-all duration-300 shadow-lg border-2 border-black"
                  >
                    <Camera size={16} className="text-white" />
                  </motion.button>
                </div>

                {/* Enhanced Profile Info */}
                <div className="flex-1 space-y-4">
                  <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-6">
                    <div className="space-y-3">
                      <div>
                        <h1 className="text-3xl lg:text-4xl font-bold bg-gradient-to-r from-white via-gray-100 to-gray-300 bg-clip-text text-transparent">
                          {user.first_name} {user.last_name}
                        </h1>
                        <div className="flex items-center gap-2 mt-2">
                          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                          <span className="text-green-400 text-sm font-medium">Active Member</span>
                        </div>
                      </div>
                      
                      <div className="space-y-2">
                        <motion.div 
                          whileHover={{ x: 4 }}
                          className="flex items-center gap-3 text-gray-300 hover:text-white transition-colors"
                        >
                          <div className="w-8 h-8 bg-blue-500/20 rounded-lg flex items-center justify-center">
                            <Phone size={16} />
                          </div>
                          <span className="font-medium">{formatPhoneDisplay(user.phone)}</span>
                        </motion.div>
                        
                        {user.email && (
                          <motion.div 
                            whileHover={{ x: 4 }}
                            className="flex items-center gap-3 text-gray-300 hover:text-white transition-colors"
                          >
                            <div className="w-8 h-8 bg-purple-500/20 rounded-lg flex items-center justify-center">
                              <Mail size={16} />
                            </div>
                            <span className="font-medium">{user.email}</span>
                          </motion.div>
                        )}
                      </div>
                    </div>

                    <motion.button
                      whileHover={{ scale: 1.02, y: -2 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={() => {
                        // Clear any errors when toggling edit mode
                        if (localError) setLocalError('');
                        setIsEditing(!isEditing);
                      }}
                      className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-500 hover:to-purple-500 text-white px-6 py-3 rounded-2xl flex items-center gap-3 transition-all duration-300 shadow-lg hover:shadow-xl border border-blue-500/20 font-medium"
                      style={{ minHeight: '48px' }}
                    >
                      <Edit3 size={18} />
                      {isEditing ? 'Cancel Edit' : 'Edit Profile'}
                    </motion.button>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Enhanced Edit Profile Form */}
          <AnimatePresence>
            {isEditing && (
              <motion.div
                initial={{ opacity: 0, height: 0, y: -20 }}
                animate={{ opacity: 1, height: 'auto', y: 0 }}
                exit={{ opacity: 0, height: 0, y: -20 }}
                transition={{ duration: 0.3 }}
                className="mb-8 overflow-hidden"
              >
                <div className="relative">
                  <div className="absolute inset-0 bg-gradient-to-br from-gray-800/20 to-gray-900/20 rounded-3xl blur-xl" />
                  <div className="relative bg-black/60 backdrop-blur-xl border border-gray-700/50 rounded-3xl p-8 shadow-2xl">
                    <div className="flex items-center gap-3 mb-6">
                      <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center">
                        <Edit3 size={18} className="text-white" />
                      </div>
                      <h2 className="text-2xl font-bold text-white">Edit Profile</h2>
                    </div>
                    
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                      {[
                        { field: 'first_name', label: 'First Name', type: 'text', required: true },
                        { field: 'last_name', label: 'Last Name', type: 'text', required: true },
                        { field: 'email', label: 'Email', type: 'email', required: true },
                        { field: 'date_of_birth', label: 'Date of Birth (Optional)', type: 'date', required: false }
                      ].map((input, idx) => (
                        <motion.div
                          key={input.field}
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: idx * 0.1 }}
                          className="space-y-2"
                        >
                          <label className="block text-sm font-semibold text-gray-300">
                            {input.label} {input.required && <span className="text-red-400">*</span>}
                          </label>
                          <input
                            type={input.type}
                            value={editForm[input.field]}
                            onChange={(e) => handleInputChange(input.field, e.target.value)}
                            required={input.required}
                            className="w-full px-4 py-3 bg-gray-900/50 border border-gray-700/50 rounded-xl text-white placeholder-gray-500 focus:border-blue-500/50 focus:ring-2 focus:ring-blue-500/20 transition-all duration-300 backdrop-blur-sm"
                            style={{ minHeight: '48px' }}
                          />
                        </motion.div>
                      ))}

                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.4 }}
                        className="lg:col-span-2 space-y-4"
                      >
                        <h3 className="text-lg font-semibold text-white flex items-center gap-2">
                          <MapPin size={16} className="text-orange-400" />
                          Shipping Address
                        </h3>
                        
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                          {/* Address Line 1 */}
                          <div className="space-y-2">
                            <label className="block text-sm font-semibold text-gray-300">
                              Address Line 1 <span className="text-red-400">*</span>
                            </label>
                            <input
                              type="text"
                              value={editForm.shipping_address.line1}
                              onChange={(e) => handleInputChange('shipping_address.line1', e.target.value)}
                              required
                              className="w-full px-4 py-3 bg-gray-900/50 border border-gray-700/50 rounded-xl text-white placeholder-gray-500 focus:border-blue-500/50 focus:ring-2 focus:ring-blue-500/20 transition-all duration-300 backdrop-blur-sm"
                              placeholder="Street address"
                            />
                          </div>
                          
                          {/* Address Line 2 */}
                          <div className="space-y-2">
                            <label className="block text-sm font-semibold text-gray-300">
                              Address Line 2
                            </label>
                            <input
                              type="text"
                              value={editForm.shipping_address.line2}
                              onChange={(e) => handleInputChange('shipping_address.line2', e.target.value)}
                              className="w-full px-4 py-3 bg-gray-900/50 border border-gray-700/50 rounded-xl text-white placeholder-gray-500 focus:border-blue-500/50 focus:ring-2 focus:ring-blue-500/20 transition-all duration-300 backdrop-blur-sm"
                              placeholder="Apartment, suite, unit, etc. (optional)"
                            />
                          </div>
                          
                          {/* City */}
                          <div className="space-y-2">
                            <label className="block text-sm font-semibold text-gray-300">
                              City <span className="text-red-400">*</span>
                            </label>
                            <input
                              type="text"
                              value={editForm.shipping_address.city}
                              onChange={(e) => handleInputChange('shipping_address.city', e.target.value)}
                              required
                              className="w-full px-4 py-3 bg-gray-900/50 border border-gray-700/50 rounded-xl text-white placeholder-gray-500 focus:border-blue-500/50 focus:ring-2 focus:ring-blue-500/20 transition-all duration-300 backdrop-blur-sm"
                              placeholder="City"
                            />
                          </div>
                          
                          {/* State */}
                          <div className="space-y-2">
                            <label className="block text-sm font-semibold text-gray-300">
                              State <span className="text-red-400">*</span>
                            </label>
                            <input
                              type="text"
                              value={editForm.shipping_address.state}
                              onChange={(e) => handleInputChange('shipping_address.state', e.target.value)}
                              required
                              className="w-full px-4 py-3 bg-gray-900/50 border border-gray-700/50 rounded-xl text-white placeholder-gray-500 focus:border-blue-500/50 focus:ring-2 focus:ring-blue-500/20 transition-all duration-300 backdrop-blur-sm"
                              placeholder="State/Province"
                            />
                          </div>
                          
                          {/* Postal Code */}
                          <div className="space-y-2">
                            <label className="block text-sm font-semibold text-gray-300">
                              Postal Code <span className="text-red-400">*</span>
                            </label>
                            <input
                              type="text"
                              value={editForm.shipping_address.postal_code}
                              onChange={(e) => handleInputChange('shipping_address.postal_code', e.target.value)}
                              required
                              className="w-full px-4 py-3 bg-gray-900/50 border border-gray-700/50 rounded-xl text-white placeholder-gray-500 focus:border-blue-500/50 focus:ring-2 focus:ring-blue-500/20 transition-all duration-300 backdrop-blur-sm"
                              placeholder="Postal/ZIP code"
                            />
                          </div>
                          
                          {/* Country */}
                          <div className="space-y-2">
                            <label className="block text-sm font-semibold text-gray-300">
                              Country <span className="text-red-400">*</span>
                            </label>
                            <select
                              value={editForm.shipping_address.country}
                              onChange={(e) => handleInputChange('shipping_address.country', e.target.value)}
                              required
                              className="w-full px-4 py-3 bg-gray-900/50 border border-gray-700/50 rounded-xl text-white focus:border-blue-500/50 focus:ring-2 focus:ring-blue-500/20 transition-all duration-300 backdrop-blur-sm"
                            >
                              <option value="India">India</option>
                              <option value="United States">United States</option>
                              <option value="Canada">Canada</option>
                              <option value="United Kingdom">United Kingdom</option>
                              <option value="Australia">Australia</option>
                              <option value="Germany">Germany</option>
                              <option value="France">France</option>
                              <option value="Japan">Japan</option>
                              <option value="China">China</option>
                              <option value="Singapore">Singapore</option>
                              <option value="UAE">UAE</option>
                            </select>
                          </div>
                        </div>
                      </motion.div>

                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.5 }}
                        className="lg:col-span-2 space-y-2"
                      >
                        <label className="block text-sm font-semibold text-gray-300">
                          Gender <span className="text-red-400">*</span>
                        </label>
                        <select
                          value={editForm.gender}
                          onChange={(e) => handleInputChange('gender', e.target.value)}
                          required
                          className="w-full px-4 py-3 bg-gray-900/50 border border-gray-700/50 rounded-xl text-white focus:border-blue-500/50 focus:ring-2 focus:ring-blue-500/20 transition-all duration-300 backdrop-blur-sm"
                          style={{ minHeight: '48px' }}
                        >
                          <option value="">Select Gender</option>
                          <option value="male">Male</option>
                          <option value="female">Female</option>
                          <option value="other">Other</option>
                          <option value="prefer_not_to_say">Prefer not to say</option>
                        </select>
                      </motion.div>
                    </div>

                    {/* Error message */}
                    {(error || localError) && (
                      <motion.div
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="mt-6 p-4 bg-red-500/10 border border-red-500/30 rounded-xl text-red-400 flex items-center gap-3 backdrop-blur-sm"
                      >
                        <AlertCircle size={18} />
                        <span className="font-medium">{error || localError}</span>
                      </motion.div>
                    )}

                    <div className="flex flex-col sm:flex-row gap-4 mt-8">
                      <motion.button
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                        onClick={handleSaveProfile}
                        disabled={isLoading}
                        className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-500 hover:to-emerald-500 disabled:from-gray-600 disabled:to-gray-700 text-white px-8 py-3 rounded-xl flex items-center justify-center gap-3 transition-all duration-300 shadow-lg font-medium"
                        style={{ minHeight: '48px', minWidth: '160px' }}
                      >
                        {isLoading ? (
                          <>
                            <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                            <span>Saving...</span>
                          </>
                        ) : (
                          <span>Save Profile</span>
                        )}
                      </motion.button>

                      <motion.button
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                        onClick={() => {
                          // Clear any errors when canceling
                          if (localError) setLocalError('');
                          setIsEditing(false);
                        }}
                        className="bg-gray-700/50 hover:bg-gray-600/50 text-white px-8 py-3 rounded-xl flex items-center justify-center gap-3 transition-all duration-300 border border-gray-600/30 font-medium"
                        style={{ minHeight: '48px', minWidth: '120px' }}
                      >
                        <X size={18} />
                        <span>Cancel</span>
                      </motion.button>
                    </div>
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Enhanced Quick Stats */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="grid grid-cols-2 lg:grid-cols-4 gap-6 mb-8"
          >
            {[
              { icon: Heart, value: totalWishlistItems, label: 'Wishlist Items', color: 'from-red-500 to-pink-500', bg: 'red-500/10' },
              { icon: ShoppingBag, value: totalItems, label: 'Cart Items', color: 'from-blue-500 to-cyan-500', bg: 'blue-500/10' },
              { icon: Package, value: 0, label: 'Orders', color: 'from-green-500 to-emerald-500', bg: 'green-500/10' },
              { icon: Shirt, value: 0, label: 'Outfits', color: 'from-purple-500 to-violet-500', bg: 'purple-500/10' }
            ].map((stat, idx) => (
              <motion.div
                key={stat.label}
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 + idx * 0.1 }}
                whileHover={{ y: -8, scale: 1.02 }}
                className="group relative"
              >
                <div className={`absolute inset-0 bg-gradient-to-br ${stat.color} opacity-0 group-hover:opacity-10 rounded-2xl blur-xl transition-opacity duration-300`} />
                <div className={`relative bg-black/40 backdrop-blur-xl border border-gray-800/50 rounded-2xl p-6 text-center transition-all duration-300 group-hover:border-gray-700/50 shadow-xl`}>
                  <div className={`w-14 h-14 bg-gradient-to-br ${stat.color} rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg`}>
                    <stat.icon size={24} className="text-white" />
                  </div>
                  <p className="text-3xl font-bold text-white mb-1">{stat.value}</p>
                  <p className="text-gray-400 text-sm font-medium">{stat.label}</p>
                </div>
              </motion.div>
            ))}
          </motion.div>

          {/* Enhanced Profile Details */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="mb-8"
          >
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-br from-gray-800/10 to-gray-900/10 rounded-3xl blur-xl" />
              <div className="relative bg-black/40 backdrop-blur-xl border border-gray-800/50 rounded-3xl p-8 shadow-2xl">
                <div className="flex items-center gap-3 mb-6">
                  <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center">
                    <User size={18} className="text-white" />
                  </div>
                  <h2 className="text-2xl font-bold text-white">Profile Details</h2>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {user.date_of_birth && (
                    <motion.div 
                      whileHover={{ scale: 1.02, y: -2 }}
                      className="flex items-center gap-4 p-4 bg-gray-900/30 rounded-2xl border border-gray-700/30 hover:border-gray-600/50 transition-all duration-300"
                    >
                      <div className="w-12 h-12 bg-blue-500/20 rounded-xl flex items-center justify-center">
                        <Calendar size={20} className="text-blue-400" />
                      </div>
                      <div>
                        <p className="text-gray-400 text-sm font-medium">Date of Birth</p>
                        <p className="text-white font-semibold">{formatDate(user.date_of_birth)}</p>
                      </div>
                    </motion.div>
                  )}

                  {user.gender && (
                    <motion.div 
                      whileHover={{ scale: 1.02, y: -2 }}
                      className="flex items-center gap-4 p-4 bg-gray-900/30 rounded-2xl border border-gray-700/30 hover:border-gray-600/50 transition-all duration-300"
                    >
                      <div className="w-12 h-12 bg-purple-500/20 rounded-xl flex items-center justify-center">
                        <User size={20} className="text-purple-400" />
                      </div>
                      <div>
                        <p className="text-gray-400 text-sm font-medium">Gender</p>
                        <p className="text-white font-semibold capitalize">{user.gender.replace('_', ' ')}</p>
                      </div>
                    </motion.div>
                  )}

                  {user.shipping_address && (
                    <motion.div 
                      whileHover={{ scale: 1.02, y: -2 }}
                      className="flex items-center gap-4 p-4 bg-gray-900/30 rounded-2xl border border-gray-700/30 hover:border-gray-600/50 transition-all duration-300"
                    >
                      <div className="w-12 h-12 bg-orange-500/20 rounded-xl flex items-center justify-center">
                        <MapPin size={20} className="text-orange-400" />
                      </div>
                      <div className="flex-1">
                        <p className="text-gray-400 text-sm font-medium">Shipping Address</p>
                        <div className="text-white font-semibold">
                          <p>{user.shipping_address.line1 || user.shipping_address.street}</p>
                          {user.shipping_address.line2 && <p>{user.shipping_address.line2}</p>}
                          <p>
                            {user.shipping_address.city}, {user.shipping_address.state} {user.shipping_address.postal_code || user.shipping_address.zip}
                          </p>
                          <p>{user.shipping_address.country}</p>
                        </div>
                      </div>
                    </motion.div>
                  )}

                  <motion.div 
                    whileHover={{ scale: 1.02, y: -2 }}
                    className="flex items-center gap-4 p-4 bg-gray-900/30 rounded-2xl border border-gray-700/30 hover:border-gray-600/50 transition-all duration-300"
                  >
                    <div className="w-12 h-12 bg-green-500/20 rounded-xl flex items-center justify-center">
                      <Award size={20} className="text-green-400" />
                    </div>
                    <div>
                      <p className="text-gray-400 text-sm font-medium">Member Since</p>
                      <p className="text-white font-semibold">{formatDate(user.created_at)}</p>
                    </div>
                  </motion.div>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Enhanced Logout Button */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
            className="text-center"
          >
            <motion.button
              whileHover={{ scale: 1.05, y: -2 }}
              whileTap={{ scale: 0.95 }}
              onClick={handleLogout}
              className="bg-gradient-to-r from-red-600 to-red-700 hover:from-red-500 hover:to-red-600 text-white px-8 py-4 rounded-2xl flex items-center gap-3 mx-auto transition-all duration-300 shadow-lg hover:shadow-xl border border-red-500/20 font-medium"
              style={{ minHeight: '52px' }}
            >
              <LogOut size={20} />
              Logout
            </motion.button>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default ProfilePage;